import { IncomingMessage, ServerResponse } from 'http';

/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  dev: {
    '/admin/': {
      target: 'http://111.6.178.34:9202',
      changeOrigin: true,
      pathRewrite: { '^/admin': '/api/admin' },
    },
  },
};
